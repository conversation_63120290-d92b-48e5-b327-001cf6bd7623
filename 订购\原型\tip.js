// 根据新的接口返回结构处理数据
let v = [];

if ($state.form.billingType === "1") {
  // 循环处理所有items
  $state.priceResp.items.forEach(item => {
    // 构建资源包类型和规格的详细信息
    const resourceType = '流量包';

    // 构建配置详情字符串，按照图片中的格式
    const resourceName = `${$state.resourceTypeOption.find(itm => itm.value === item.resourceType)?.label || '流量包'}`
    let configDetail = [
      `资源包类型：${resourceName}`,
      `资源包规格：${item.resourceUint}`,
      `加速区域：${item.areaName}`
    ];

    // 如果有计费区域信息且不是中国内地，添加计费区域
    if (item.billingArea !== 0 && item.areaName !== '中国内地' && resourceName === '流量包') {
      const label = `${$state.overseaRegionOptions.find(itm => itm.value === item.overseaRegion)?.label}`
      if (label) configDetail.push(`计费区域：${label}`);

    }

    // 添加购买时长
    configDetail.push(`购买时长：${item.peroid}年`);

    // 将配置详情数组转换为字符串，每项用换行符分隔
    const detailString = configDetail.join(' | ');

    // 添加到数据项
    v.push({
      name: "全站加速资源包",
      detail: detailString,
      count: `${item.purchaseNum}`,
      price: Number(item.finalPrice)
    });
  });
} else {

}


// 调用显示详情的方法
$refs['fa78f38f'].showDetail(v);
