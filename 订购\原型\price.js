let v;
if ($state.form.billingType === "1") {
  // 根据新的接口返回结构处理数据
  const item = $state.data.items[0]; // 获取第一个项目

  // 构建资源包类型和规格的详细信息
  const resourceType = item.resourceType === 'ICDN_FLOW_P' ? '流量包' : item.resourceName;
  const resourceDetail = `${item.label.split('-')[1] || resourceType} | ${item.resourceUint} | ${item.areaName}`;

  // 确定购买时长的显示格式
  let timeUnit = '个月';
  if ($state.buyTotalTime >= 12) {
    timeUnit = '年';
  }

  // 构建数据项
  v = [
    {
      name: "规格",
      detail: resourceDetail,
      count: `${item.peroid}${timeUnit}，${item.purchaseNum}个`,
      price: Number(item.finalPrice)
    }
  ];

  // 如果有其他配置项，可以在这里添加
  // 例如，如果有区域特定配置
  if (item.billingArea !== 0 && item.billingArea) {
    // 假设有计费区域信息
    v.push({
      name: "计费区域",
      detail: item.billingAreaDetail || "详细区域信息",
      count: `${item.peroid}${timeUnit}，${item.purchaseNum}个`,
      price: 0 // 通常区域信息不单独计费
    });
  }
} else {
  // 按量计费逻辑 - 基于 $state.priceResp 处理费用明细的格式化显示

  // 从 asd.js 导入 saleList 配置信息
  const saleList = [
    { "label": "全站加速-日带宽峰值计费-中国内地-2022", "value": "ec022991c7924d25af8e00e9aeb87a58" },
    { "label": "全站加速-流量计费-2022", "value": "a10029274de34c3880067c4f9a760ae1" },
    { "label": "上传加速-日带宽峰值计费", "value": "467877caf02f41b68aecd6665e409e0e" },
    { "label": "上传加速-流量计费", "value": "9785a0a6230447a0a6f3b674529cf453" },
    { "label": "websocket加速-日带宽峰值计费", "value": "fab645fc2a16483e99cdb7d907cc44d8" },
    { "label": "websocket加速-流量计费", "value": "9eaed8d377ae45b3b2c02bc403330a46" },
    { "label": "全站加速-动态请求数计费", "value": "8b809ccdac024f86b3cb54646fcf6884" },
    { "label": "全站加速-静态https请求数计费", "value": "4d1f7565d5bc47478d402a465bbfc9d3" },
    { "label": "全站加速-日带宽峰值计费-全球（不含中国内地）-北美", "value": "c5e83d631d144d2a8f68b37c10a8c720" },
    { "label": "全站加速-日带宽峰值计费-全球（不含中国内地）-欧洲", "value": "a83e392970ae42dfa4bd1f1e01acd7a2" },
    { "label": "全站加速-日带宽峰值计费-全球（不含中国内地）-亚太1区", "value": "9419fd0a096a469f9bed8a220883c503" },
    { "label": "全站加速-日带宽峰值计费-全球（不含中国内地）-亚太2区", "value": "959ae6babd0c45b4b071f80b4ffe9be1" },
    { "label": "全站加速-日带宽峰值计费-全球（不含中国内地）-亚太3区", "value": "f59f6267431d41b7b3f8926ffe930331" },
    { "label": "全站加速-日带宽峰值计费-全球（不含中国内地）-中东非", "value": "135230be2bb1432ba28a5878439e9b81" },
    { "label": "全站加速-日带宽峰值计费-全球（不含中国内地）-南美", "value": "92ee7d7b57f443acae463a95e81a88da" },
    { "label": "全站加速-流量计费-全球（不含中国内地）-北美", "value": "89b7380c6c574068a24a0933587472da" },
    { "label": "全站加速-流量计费-全球（不含中国内地）-欧洲", "value": "30d2196ed08c44ad8a2db676a712954c" },
    { "label": "全站加速-流量计费-全球（不含中国内地）-亚太1区", "value": "40c380c9092e4a69b0ea02e4f74ea5ef" },
    { "label": "全站加速-流量计费-全球（不含中国内地）-亚太2区", "value": "480b153d26cd4095bd7b1c15f619cf05" },
    { "label": "全站加速-流量计费-全球（不含中国内地）-亚太3区", "value": "632ccfab50344cd5ada2474a2aec577e" },
    { "label": "全站加速-流量计费-全球（不含中国内地）-中东非", "value": "087a357679ca454a85afd757f9f37fe7" },
    { "label": "全站加速-流量计费-全球（不含中国内地）-南美", "value": "f25927af761c4a99abe2867c95ce29c3" },
    { "label": "全站加速-动态请求数计费-全球（不含中国内地）", "value": "835a771e26f348cd800909ad56ff7a66" },
    { "label": "全站加速-静态https请求数计费-全球（不含中国内地）", "value": "ed6e8403b11a498bb3c7d7cb866f371e" }
  ];

  // 辅助函数：根据 itemId 查找对应的配置信息
  const findSaleConfig = (itemId) => {
    return saleList.find(sale => sale.value === itemId);
  };

  // 辅助函数：解析计费项类型
  const parseServiceType = (label) => {
    if (label.includes('上传加速')) return '全站加速-上传加速';
    if (label.includes('websocket加速')) return '全站加速-websocket加速';
    return '全站加速';
  };

  // 辅助函数：解析计费方式
  const parseBillingMethod = (label) => {
    if (label.includes('日带宽峰值计费')) return '日带宽峰值';
    if (label.includes('流量计费')) return '流量';
    if (label.includes('动态请求数计费')) return '动态请求数';
    if (label.includes('静态https请求数计费')) return '静态HTTPS请求数';
    return '';
  };

  // 辅助函数：解析加速区域
  const parseRegion = (label) => {
    if (label.includes('中国内地')) return '中国内地';
    if (label.includes('全球（不含中国内地）')) return '全球（不含中国内地）';
    return '';
  };

  // 辅助函数：解析具体区域（用于全球区域）
  const parseSpecificRegion = (label) => {
    if (label.includes('-北美')) return '北美';
    if (label.includes('-欧洲')) return '欧洲';
    if (label.includes('-亚太1区')) return '亚太1区';
    if (label.includes('-亚太2区')) return '亚太2区';
    if (label.includes('-亚太3区')) return '亚太3区';
    if (label.includes('-中东非')) return '中东/非洲';
    if (label.includes('-南美')) return '南美';
    return '';
  };

  // 辅助函数：格式化数值和单位
  const formatValueWithUnit = (value, unit) => {
    if (!value || value === 0) return '-';
    return `${value} ${unit}`;
  };

  // 辅助函数：获取购买时长显示
  const getPurchaseDuration = () => {
    const period = $state.form.purchasePeriod || 1;
    const unit = $state.form.purchasePeriodUnit || '月';
    return `${period} ${unit}`;
  };

  // 辅助函数：从表单数据获取带宽/流量信息
  const getBandwidthFlowInfo = (serviceType, billingMethod, region) => {
    const billingArea = $state.form.billingArea;
    const domainType = $state.form.domainType;
    const isMainLand = billingArea === 0;
    const isGlobal = billingArea === 1;
    const isDayPeak = billingMethod === '日带宽峰值';
    const isFlow = billingMethod === '流量';

    if (isMainLand) {
      // 中国内地：显示单一数值
      if (isDayPeak) {
        const value = $state.form.dayPeakNum;
        const unit = $state.form.dayPeakUnit;
        return formatValueWithUnit(value, unit);
      } else if (isFlow) {
        const value = $state.form.dayFlowNum;
        const unit = $state.form.dayFlowUnit;
        return formatValueWithUnit(value, unit);
      }
    } else if (isGlobal) {
      // 全球（不含中国内地）：显示7个大区的明细数据
      const regionDetails = [];

      if (isDayPeak) {
        // 日带宽峰值计费的各区域数据
        if ($state.form.dayPeakNum_USA) {
          regionDetails.push(`北美：${formatValueWithUnit($state.form.dayPeakNum_USA, $state.form.dayPeakUnit_USA)}`);
        }
        if ($state.form.dayPeakNum_EU) {
          regionDetails.push(`欧洲：${formatValueWithUnit($state.form.dayPeakNum_EU, $state.form.dayPeakUnit_EU)}`);
        }
        if ($state.form.dayPeakNum_ASIA_1) {
          regionDetails.push(`亚太1区：${formatValueWithUnit($state.form.dayPeakNum_ASIA_1, $state.form.dayPeakUnit_ASIA_1)}`);
        }
        if ($state.form.dayPeakNum_ASIA_2) {
          regionDetails.push(`亚太2区：${formatValueWithUnit($state.form.dayPeakNum_ASIA_2, $state.form.dayPeakUnit_ASIA_2)}`);
        }
        if ($state.form.dayPeakNum_ASIA_3) {
          regionDetails.push(`亚太3区：${formatValueWithUnit($state.form.dayPeakNum_ASIA_3, $state.form.dayPeakUnit_ASIA_3)}`);
        }
        if ($state.form.dayPeakNum_AFRIA) {
          regionDetails.push(`中东/非洲：${formatValueWithUnit($state.form.dayPeakNum_AFRIA, $state.form.dayPeakUnit_AFRIA)}`);
        }
        if ($state.form.dayPeakNum_SA) {
          regionDetails.push(`南美：${formatValueWithUnit($state.form.dayPeakNum_SA, $state.form.dayPeakUnit_SA)}`);
        }
      } else if (isFlow) {
        // 流量计费的各区域数据
        if ($state.form.dayFlowNum_USA) {
          regionDetails.push(`北美：${formatValueWithUnit($state.form.dayFlowNum_USA, $state.form.dayFlowUnit_USA)}`);
        }
        if ($state.form.dayFlowNum_EU) {
          regionDetails.push(`欧洲：${formatValueWithUnit($state.form.dayFlowNum_EU, $state.form.dayFlowUnit_EU)}`);
        }
        if ($state.form.dayFlowNum_ASIA_1) {
          regionDetails.push(`亚太1区：${formatValueWithUnit($state.form.dayFlowNum_ASIA_1, $state.form.dayFlowUnit_ASIA_1)}`);
        }
        if ($state.form.dayFlowNum_ASIA_2) {
          regionDetails.push(`亚太2区：${formatValueWithUnit($state.form.dayFlowNum_ASIA_2, $state.form.dayFlowUnit_ASIA_2)}`);
        }
        if ($state.form.dayFlowNum_ASIA_3) {
          regionDetails.push(`亚太3区：${formatValueWithUnit($state.form.dayFlowNum_ASIA_3, $state.form.dayFlowUnit_ASIA_3)}`);
        }
        if ($state.form.dayFlowNum_AFRIA) {
          regionDetails.push(`中东/非洲：${formatValueWithUnit($state.form.dayFlowNum_AFRIA, $state.form.dayFlowUnit_AFRIA)}`);
        }
        if ($state.form.dayFlowNum_SA) {
          regionDetails.push(`南美：${formatValueWithUnit($state.form.dayFlowNum_SA, $state.form.dayFlowUnit_SA)}`);
        }
      }

      return regionDetails.length > 0 ? regionDetails.join('\n') : '-';
    }

    return '-';
  };

  // 初始化结果数组
  v = [];

  // 遍历 $state.priceResp.items 数组处理费用明细
  if ($state.priceResp && $state.priceResp.items && Array.isArray($state.priceResp.items)) {
    // 按服务类型和计费方式分组处理
    const processedGroups = new Set();

    $state.priceResp.items.forEach(item => {
      // 根据 itemId 查找对应的配置信息
      const saleConfig = findSaleConfig(item.itemId);

      if (saleConfig) {
        const label = saleConfig.label;

        // 解析配置信息
        const serviceType = parseServiceType(label);
        const billingMethod = parseBillingMethod(label);
        const region = parseRegion(label);

        // 创建分组标识
        const groupKey = `${serviceType}-${billingMethod}-${region}`;

        // 如果是全球区域的带宽/流量计费，需要特殊处理
        if (region === '全球（不含中国内地）' &&
            (billingMethod === '日带宽峰值' || billingMethod === '流量') &&
            !processedGroups.has(groupKey)) {

          processedGroups.add(groupKey);

          // 构建配置详情字符串
          let configDetails = [];

          // 添加计费方式
          configDetails.push(billingMethod);

          // 添加加速区域
          configDetails.push(region);

          // 构建全球区域的详细信息
          const globalRegionDetails = getBandwidthFlowInfo(serviceType, billingMethod, region);
          if (globalRegionDetails && globalRegionDetails !== '-') {
            configDetails.push(globalRegionDetails);
          }

          // 添加动态请求数信息
          if ($state.form.dayPeakDynamicRequest) {
            configDetails.push(`日动态请求数：${$state.form.dayPeakDynamicRequest} 万次`);
          }

          // 添加静态HTTPS请求数信息
          const httpsRequests = $state.form.dayPeakStaticHTTPS || $state.form.dayPeakStaticQUIC || 0;
          if (httpsRequests > 0) {
            configDetails.push(`日静态HTTPS请求数：${httpsRequests} 万次`);
          }

          // 添加购买时长
          configDetails.push(`购买时长：${getPurchaseDuration()}`);

          // 添加购买量（按量计费默认显示 -）
          configDetails.push('购买量：-');

          // 构建最终的详情字符串
          const detail = configDetails.join(' | ');

          // 计算该组的总价格
          let totalPrice = 0;
          $state.priceResp.items.forEach(groupItem => {
            const groupSaleConfig = findSaleConfig(groupItem.itemId);
            if (groupSaleConfig &&
                parseServiceType(groupSaleConfig.label) === serviceType &&
                parseBillingMethod(groupSaleConfig.label) === billingMethod &&
                parseRegion(groupSaleConfig.label) === region) {
              totalPrice += Number(groupItem.finalPrice || groupItem.totalPrice || 0);
            }
          });

          // 添加到结果数组
          v.push({
            name: serviceType,
            detail: detail,
            count: getPurchaseDuration(),
            price: totalPrice
          });

        } else if (region === '中国内地' && !processedGroups.has(groupKey)) {
          // 处理中国内地区域
          processedGroups.add(groupKey);

          // 构建配置详情字符串
          let configDetails = [];

          // 添加计费方式
          if (billingMethod) {
            configDetails.push(billingMethod);
          }

          // 添加加速区域
          if (region) {
            configDetails.push(region);
          }

          // 构建带宽/流量显示信息
          const bandwidthFlowInfo = getBandwidthFlowInfo(serviceType, billingMethod, region);
          if (bandwidthFlowInfo && bandwidthFlowInfo !== '-') {
            configDetails.push(bandwidthFlowInfo);
          }

          // 添加动态请求数信息
          if ($state.form.dayPeakDynamicRequest) {
            configDetails.push(`日动态请求数：${$state.form.dayPeakDynamicRequest} 万次`);
          }

          // 添加静态HTTPS请求数信息
          const httpsRequests = $state.form.dayPeakStaticHTTPS || $state.form.dayPeakStaticQUIC || 0;
          if (httpsRequests > 0) {
            configDetails.push(`日静态HTTPS请求数：${httpsRequests} 万次`);
          }

          // 添加购买时长
          configDetails.push(`购买时长：${getPurchaseDuration()}`);

          // 添加购买量（按量计费默认显示 -）
          configDetails.push('购买量：-');

          // 构建最终的详情字符串
          const detail = configDetails.join(' | ');

          // 添加到结果数组
          v.push({
            name: serviceType,
            detail: detail,
            count: getPurchaseDuration(),
            price: Number(item.finalPrice || item.totalPrice || 0)
          });
        }
      }
    });
  }
}

// 调用显示详情的方法
$refs['a49fb6ba'].showDetail(v);
